# Master-Slave MySQL Database with Spring Boot

This project demonstrates a master-slave MySQL database setup using Docker containers and Spring Boot with JPA for read/write splitting.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Spring Boot Application                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   UserService   │    │ UserController  │                    │
│  │                 │    │                 │                    │
│  │ - Write Ops     │    │ - REST API      │                    │
│  │ - Read Ops      │    │ - CRUD Ops      │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Master Repository│    │Slave Repository │                    │
│  │                 │    │                 │                    │
│  │ - Write Ops     │    │ - Read Ops      │                    │
│  │ - CUD Operations│    │ - Select Queries│                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Master DataSource│    │Slave DataSource │                    │
│  │                 │    │                 │                    │
│  │ - Port: 3306    │    │ - Port: 3307    │                    │
│  │ - Read/Write    │    │ - Read Only     │                    │
│  └─────────────────┘    └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
                    │                    │
                    ▼                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Docker Network                               │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  MySQL Master   │    │  MySQL Slave    │                    │
│  │                 │────┤                 │                    │
│  │ - Server ID: 1  │    │ - Server ID: 2  │                    │
│  │ - Binary Log    │    │ - Relay Log     │                    │
│  │ - Port: 3306    │    │ - Port: 3306    │                    │
│  │ - Read/Write    │    │ - Read Only     │                    │
│  └─────────────────┘    └─────────────────┘                    │
│         │                        │                             │
│         ▼                        ▼                             │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │Master Data Vol. │    │Slave Data Vol.  │                    │
│  └─────────────────┘    └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
```

## Features

- **Master-Slave Replication**: Automatic data replication from master to slave
- **Read/Write Splitting**: Write operations go to master, read operations go to slave
- **Connection Pooling**: HikariCP for efficient database connections
- **Transaction Management**: Separate transaction managers for master and slave
- **Comprehensive Logging**: Detailed logging for database operations
- **REST API**: Full CRUD operations via REST endpoints
- **Demo Component**: Automatic demonstration of read/write splitting

## Prerequisites

1. **Docker Desktop**: Must be installed and running
2. **Java 21**: Required for Spring Boot 3.5.0
3. **Gradle**: For building the application

## Quick Start

### 1. Start Docker Desktop
Make sure Docker Desktop is running on your system.

### 2. Start MySQL Containers
```bash
docker-compose up -d
```

### 3. Setup Replication
**Windows:**
```cmd
setup-replication.bat
```

**Linux/Mac:**
```bash
chmod +x setup-replication.sh
./setup-replication.sh
```

### 4. Build and Run Application
```bash
./gradlew bootRun
```

## Database Configuration

### Master Database (Port 3306)
- **Purpose**: Handle all write operations (INSERT, UPDATE, DELETE)
- **User**: `app_user` / `app_password`
- **Features**: Binary logging enabled for replication

### Slave Database (Port 3307)
- **Purpose**: Handle all read operations (SELECT)
- **User**: `readonly_user` / `readonly_password`
- **Features**: Read-only mode, replication from master

## API Endpoints

### Write Operations (Master DB)
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user
- `PATCH /api/users/{id}/name?name=NewName` - Update user name

### Read Operations (Slave DB)
- `GET /api/users` - Get all users
- `GET /api/users/{id}` - Get user by ID
- `GET /api/users/email/{email}` - Get user by email
- `GET /api/users/search?name=searchTerm` - Search users by name
- `GET /api/users/count` - Get user count
- `GET /api/users/recent` - Get users ordered by creation date

## Testing the Setup

### 1. Check Replication Status
```bash
docker exec mysql-slave mysql -uroot -prootpassword -e "SHOW SLAVE STATUS\G"
```

### 2. Test Write Operation
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>"}'
```

### 3. Test Read Operation
```bash
curl http://localhost:8080/api/users
```

### 4. Check Logs
The application logs will show which database (MASTER/SLAVE) is being used for each operation.

## Project Structure

```
src/main/java/com/dh/
├── config/
│   ├── DatabaseConfig.java           # Database configuration
│   ├── MasterRepositoryConfig.java   # Master repository config
│   └── SlaveRepositoryConfig.java    # Slave repository config
├── controller/
│   └── UserController.java           # REST API endpoints
├── demo/
│   └── MasterSlaveDemo.java          # Demo component
├── entity/
│   └── User.java                     # JPA entity
├── repository/
│   ├── master/
│   │   └── UserMasterRepository.java # Write operations
│   └── slave/
│       └── UserSlaveRepository.java  # Read operations
├── service/
│   └── UserService.java              # Business logic
└── SlaveDbLessonApplication.java     # Main application
```

## Key Components

### DatabaseConfig
- Configures two separate DataSources (master and slave)
- Sets up EntityManagerFactory for each database
- Configures transaction managers

### UserService
- Implements read/write splitting logic
- Uses `@Transactional` with specific transaction managers
- Comprehensive logging for operations

### Repository Layer
- Separate repositories for master and slave operations
- Master repository: Write operations
- Slave repository: Read operations with `readOnly = true`

## Monitoring and Troubleshooting

### Check Container Status
```bash
docker-compose ps
```

### View Container Logs
```bash
docker-compose logs mysql-master
docker-compose logs mysql-slave
```

### Connect to MySQL Containers
```bash
# Master
docker exec -it mysql-master mysql -uroot -prootpassword

# Slave
docker exec -it mysql-slave mysql -uroot -prootpassword
```

### Verify Replication
```sql
-- On Master
SHOW MASTER STATUS;

-- On Slave
SHOW SLAVE STATUS\G
```

## Cleanup

To stop and remove all containers:
```bash
docker-compose down -v
```

## Notes

- The demo component runs automatically on application startup
- All write operations are logged as going to MASTER database
- All read operations are logged as going to SLAVE database
- Replication typically happens within seconds
- The application uses connection pooling for optimal performance
