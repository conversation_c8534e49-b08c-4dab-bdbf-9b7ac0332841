version: '3.8'

services:
  mysql-master:
    image: mysql:8.0
    container_name: mysql-master
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: masterslavedb
      MYSQL_USER: dbuser
      MYSQL_PASSWORD: dbpassword
    ports:
      - "3306:3306"
    volumes:
      - ./mysql-config/master/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./mysql-init/master:/docker-entrypoint-initdb.d
      - mysql-master-data:/var/lib/mysql
    networks:
      - mysql-network
    command: --server-id=1 --log-bin=mysql-bin --binlog-do-db=masterslavedb

  mysql-slave:
    image: mysql:8.0
    container_name: mysql-slave
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: masterslavedb
      MYSQL_USER: dbuser
      MYSQL_PASSWORD: dbpassword
    ports:
      - "3307:3306"
    volumes:
      - ./mysql-config/slave/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./mysql-init/slave:/docker-entrypoint-initdb.d
      - mysql-slave-data:/var/lib/mysql
    networks:
      - mysql-network
    depends_on:
      - mysql-master
    command: --server-id=2 --relay-log=mysql-relay-bin --log-bin=mysql-bin --binlog-do-db=masterslavedb

volumes:
  mysql-master-data:
  mysql-slave-data:

networks:
  mysql-network:
    driver: bridge
