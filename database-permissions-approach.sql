-- Database-Level Permission Control
-- This approach eliminates the need for readOnly = true in application code

-- MASTER DATABASE USERS
-- Full access user for master (can read and write)
CREATE USER 'master_user'@'%' IDENTIFIED BY 'master_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON masterslavedb.* TO 'master_user'@'%';

-- SLAVE DATABASE USERS  
-- Read-only user for slave (database enforces read-only)
CREATE USER 'slave_user'@'%' IDENTIFIED BY 'slave_password';
GRANT SELECT ON masterslavedb.* TO 'slave_user'@'%';
-- Note: No INSERT, UPDATE, DELETE permissions!

-- If application tries to write to slave, database will reject with error:
-- "INSERT command denied to user 'slave_user'@'localhost' for table 'users'"

FLUSH PRIVILEGES;
