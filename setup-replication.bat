@echo off
echo Setting up MySQL Master-Slave Replication...

echo Waiting for master database to be ready...
:wait_master
docker exec mysql-master mysql -uroot -prootpassword -e "SELECT 1" >nul 2>&1
if errorlevel 1 (
    echo Waiting for master database...
    timeout /t 2 /nobreak >nul
    goto wait_master
)

echo Waiting for slave database to be ready...
:wait_slave
docker exec mysql-slave mysql -uroot -prootpassword -e "SELECT 1" >nul 2>&1
if errorlevel 1 (
    echo Waiting for slave database...
    timeout /t 2 /nobreak >nul
    goto wait_slave
)

echo Getting master status...
docker exec mysql-master mysql -uroot -prootpassword -e "SHOW MASTER STATUS\G" > master_status.tmp

for /f "tokens=2" %%a in ('findstr "File:" master_status.tmp') do set MASTER_FILE=%%a
for /f "tokens=2" %%a in ('findstr "Position:" master_status.tmp') do set MASTER_POSITION=%%a

echo Master File: %MASTER_FILE%
echo Master Position: %MASTER_POSITION%

echo Configuring slave replication...
docker exec mysql-slave mysql -uroot -prootpassword -e "STOP SLAVE; CHANGE MASTER TO MASTER_HOST='mysql-master', MASTER_USER='replicator', MASTER_PASSWORD='replicator_password', MASTER_LOG_FILE='%MASTER_FILE%', MASTER_LOG_POS=%MASTER_POSITION%; START SLAVE;"

echo Checking slave status...
docker exec mysql-slave mysql -uroot -prootpassword -e "SHOW SLAVE STATUS\G" | findstr /C:"Slave_IO_Running" /C:"Slave_SQL_Running" /C:"Last_Error"

del master_status.tmp
echo Replication setup completed!
