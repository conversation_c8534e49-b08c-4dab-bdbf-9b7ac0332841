[mysqld]
# Slave configuration
server-id = 2
relay-log = mysql-relay-bin
log-bin = mysql-bin
binlog-format = ROW
binlog-do-db = masterslavedb

# Read-only mode (except for replication)
read-only = 1
super-read-only = 1

# Relay log settings
relay-log-recovery = 1
relay-log-purge = 1

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Connection settings
max_connections = 200
