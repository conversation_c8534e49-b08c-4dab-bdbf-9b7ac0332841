#!/bin/bash

echo "Setting up MySQL Master-Slave Replication..."

# Wait for master to be ready
echo "Waiting for master database to be ready..."
until docker exec mysql-master mysql -uroot -prootpassword -e "SELECT 1" >/dev/null 2>&1; do
    echo "Waiting for master database..."
    sleep 2
done

# Wait for slave to be ready
echo "Waiting for slave database to be ready..."
until docker exec mysql-slave mysql -uroot -prootpassword -e "SELECT 1" >/dev/null 2>&1; do
    echo "Waiting for slave database..."
    sleep 2
done

# Get master status
echo "Getting master status..."
MASTER_STATUS=$(docker exec mysql-master mysql -uroot -prootpassword -e "SHOW MASTER STATUS\G")
MASTER_FILE=$(echo "$MASTER_STATUS" | grep "File:" | awk '{print $2}')
MASTER_POSITION=$(echo "$MASTER_STATUS" | grep "Position:" | awk '{print $2}')

echo "Master File: $MASTER_FILE"
echo "Master Position: $MASTER_POSITION"

# Configure slave
echo "Configuring slave replication..."
docker exec mysql-slave mysql -uroot -prootpassword -e "
STOP SLAVE;
CHANGE MASTER TO
    MASTER_HOST='mysql-master',
    MASTER_USER='replicator',
    MASTER_PASSWORD='replicator_password',
    MASTER_LOG_FILE='$MASTER_FILE',
    MASTER_LOG_POS=$MASTER_POSITION;
START SLAVE;
"

# Check slave status
echo "Checking slave status..."
docker exec mysql-slave mysql -uroot -prootpassword -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error)"

echo "Replication setup completed!"
