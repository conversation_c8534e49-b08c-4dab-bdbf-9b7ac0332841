spring.application.name=slaveDBLesson

# Master Database Configuration (Write Operations)
spring.datasource.master.jdbc-url=******************************************************************************************************
spring.datasource.master.username=app_user
spring.datasource.master.password=app_password
spring.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.master.hikari.maximum-pool-size=20
spring.datasource.master.hikari.minimum-idle=5
spring.datasource.master.hikari.connection-timeout=20000
spring.datasource.master.hikari.idle-timeout=300000
spring.datasource.master.hikari.max-lifetime=1200000

# Slave Database Configuration (Read Operations)
spring.datasource.slave.jdbc-url=******************************************************************************************************
spring.datasource.slave.username=readonly_user
spring.datasource.slave.password=readonly_password
spring.datasource.slave.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.slave.hikari.maximum-pool-size=20
spring.datasource.slave.hikari.minimum-idle=5
spring.datasource.slave.hikari.connection-timeout=20000
spring.datasource.slave.hikari.idle-timeout=300000
spring.datasource.slave.hikari.max-lifetime=1200000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Logging Configuration
logging.level.com.dh=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Server Configuration
server.port=8080
