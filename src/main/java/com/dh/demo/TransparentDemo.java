package com.dh.demo;

import com.dh.entity.User;
import com.dh.service.TransparentUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Transparent Master-Slave Demo
 * 
 * This demo shows how transparent read/write splitting works:
 * - Application code doesn't know about master/slave
 * - Routing happens automatically based on @Transactional annotations
 * - readOnly=true → SLAVE database
 * - readOnly=false (default) → MASTER database
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(2) // Run after the original demo
public class TransparentDemo implements CommandLineRunner {
    
    private final TransparentUserService transparentUserService;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("🔄 === TRANSPARENT MASTER-SLAVE DEMO STARTING ===");
        
        // Wait a bit for the system to be ready
        Thread.sleep(3000);
        
        try {
            demonstrateTransparentRouting();
        } catch (Exception e) {
            log.error("❌ Transparent demo failed", e);
        }
        
        log.info("🔄 === TRANSPARENT MASTER-SLAVE DEMO COMPLETED ===");
    }
    
    private void demonstrateTransparentRouting() {
        log.info("\n" + "=".repeat(70));
        log.info("🔄 DEMONSTRATING TRANSPARENT READ/WRITE SPLITTING");
        log.info("=".repeat(70));
        
        // 1. Read operation - should go to SLAVE
        log.info("\n--- STEP 1: Transparent READ operation (→ SLAVE) ---");
        List<User> users = transparentUserService.getAllUsers();
        log.info("📊 Found {} users using transparent routing", users.size());
        
        // 2. Write operation - should go to MASTER
        log.info("\n--- STEP 2: Transparent WRITE operation (→ MASTER) ---");
        User newUser = new User();
        newUser.setName("Transparent User");
        newUser.setEmail("<EMAIL>");
        
        User createdUser = transparentUserService.createUser(newUser);
        log.info("📊 Created user with ID: {} using transparent routing", createdUser.getId());
        
        // 3. Another read operation - should go to SLAVE
        log.info("\n--- STEP 3: Another transparent READ operation (→ SLAVE) ---");
        long userCount = transparentUserService.getUserCount();
        log.info("📊 Total user count: {} using transparent routing", userCount);
        
        // 4. Update operation - should go to MASTER
        log.info("\n--- STEP 4: Transparent UPDATE operation (→ MASTER) ---");
        if (createdUser.getId() != null) {
            int updateResult = transparentUserService.updateUserName(
                createdUser.getId(), "Updated Transparent User");
            log.info("📊 Update result: {} rows affected using transparent routing", updateResult);
        }
        
        // 5. Search operation - should go to SLAVE
        log.info("\n--- STEP 5: Transparent SEARCH operation (→ SLAVE) ---");
        List<User> searchResults = transparentUserService.getUsersByNameContaining("Transparent");
        log.info("📊 Found {} users with 'Transparent' in name using transparent routing", 
                searchResults.size());
        
        log.info("\n" + "=".repeat(70));
        log.info("🎯 TRANSPARENT ROUTING SUMMARY:");
        log.info("✅ Application code is completely unaware of master/slave setup");
        log.info("✅ Routing happens automatically based on transaction type");
        log.info("✅ @Transactional(readOnly=true) → SLAVE database");
        log.info("✅ @Transactional → MASTER database");
        log.info("✅ No need for separate repositories or services");
        log.info("✅ Single codebase works with any database architecture");
        log.info("=".repeat(70));
    }
}
