package com.dh.demo;

import com.dh.entity.User;
import com.dh.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class MasterSlaveDemo implements CommandLineRunner {
    
    private final UserService userService;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== MASTER-SLAVE DATABASE DEMO STARTING ===");
        
        // Wait a bit for databases to be ready
        Thread.sleep(5000);
        
        try {
            demonstrateReadWriteSplitting();
        } catch (Exception e) {
            log.error("Demo failed to run completely", e);
        }
        
        log.info("=== MASTER-SLAVE DATABASE DEMO COMPLETED ===");
    }
    
    private void demonstrateReadWriteSplitting() {
        log.info("\n" + "=".repeat(60));
        log.info("DEMONSTRATING MASTER-SLAVE READ/WRITE SPLITTING");
        log.info("=".repeat(60));
        
        // 1. Read initial data from slave
        log.info("\n--- STEP 1: Reading initial data from SLAVE database ---");
        List<User> initialUsers = userService.getAllUsers();
        log.info("Initial user count: {}", initialUsers.size());
        
        // 2. Create new user in master
        log.info("\n--- STEP 2: Creating new user in MASTER database ---");
        User newUser = new User();
        newUser.setName("Demo User");
        newUser.setEmail("<EMAIL>");
        
        User createdUser = userService.createUser(newUser);
        log.info("Created user with ID: {}", createdUser.getId());
        
        // 3. Wait for replication (in real scenario, there might be a small delay)
        log.info("\n--- STEP 3: Waiting for replication to complete ---");
        try {
            Thread.sleep(2000); // Wait 2 seconds for replication
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 4. Read updated data from slave
        log.info("\n--- STEP 4: Reading updated data from SLAVE database ---");
        List<User> updatedUsers = userService.getAllUsers();
        log.info("Updated user count: {}", updatedUsers.size());
        
        // 5. Search for the new user in slave
        log.info("\n--- STEP 5: Searching for new user in SLAVE database ---");
        List<User> foundUsers = userService.getUsersByNameContaining("Demo");
        log.info("Found {} users with 'Demo' in name", foundUsers.size());
        
        // 6. Update user in master
        log.info("\n--- STEP 6: Updating user in MASTER database ---");
        if (createdUser.getId() != null) {
            int updateResult = userService.updateUserName(createdUser.getId(), "Updated Demo User");
            log.info("Update result: {} rows affected", updateResult);
        }
        
        // 7. Wait for replication again
        log.info("\n--- STEP 7: Waiting for update replication ---");
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 8. Read the updated user from slave
        log.info("\n--- STEP 8: Reading updated user from SLAVE database ---");
        if (createdUser.getId() != null) {
            userService.getUserById(createdUser.getId()).ifPresent(user -> 
                log.info("Updated user name: {}", user.getName())
            );
        }
        
        // 9. Get user count from slave
        log.info("\n--- STEP 9: Getting final user count from SLAVE database ---");
        long finalCount = userService.getUserCount();
        log.info("Final user count: {}", finalCount);
        
        // 10. Get recent users from slave
        log.info("\n--- STEP 10: Getting recent users from SLAVE database ---");
        List<User> recentUsers = userService.getUsersOrderByCreatedDate();
        log.info("Recent users count: {}", recentUsers.size());
        recentUsers.forEach(user -> 
            log.info("Recent user: {} - {}", user.getName(), user.getEmail())
        );
        
        log.info("\n" + "=".repeat(60));
        log.info("DEMO SUMMARY:");
        log.info("- All WRITE operations were performed on MASTER database");
        log.info("- All READ operations were performed on SLAVE database");
        log.info("- Data was successfully replicated from MASTER to SLAVE");
        log.info("=".repeat(60));
    }
}
