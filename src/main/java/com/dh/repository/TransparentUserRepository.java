package com.dh.repository;

import com.dh.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Transparent User Repository
 * Uses dynamic routing - no need to specify master/slave
 * The routing happens automatically based on transaction type
 */
@Repository
public interface TransparentUserRepository extends JpaRepository<User, Long> {
    
    // Read operations (will automatically go to SLAVE in read-only transactions)
    Optional<User> findByEmail(String email);
    
    List<User> findByNameContaining(String name);
    
    @Query("SELECT COUNT(u) FROM User u")
    long countAllUsers();
    
    @Query("SELECT u FROM User u ORDER BY u.createdAt DESC")
    List<User> findAllOrderByCreatedAtDesc();
    
    // Write operations (will automatically go to MASTER in read-write transactions)
    @Modifying
    @Query("UPDATE User u SET u.name = :name WHERE u.id = :id")
    int updateUserName(@Param("id") Long id, @Param("name") String name);
    
    @Modifying
    @Query("UPDATE User u SET u.email = :email WHERE u.id = :id")
    int updateUserEmail(@Param("id") Long id, @Param("email") String email);
}
