package com.dh.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

//@Configuration // Disabled in favor of TransparentJpaConfig
//        basePackages = "com.dh.repository.master",
//        entityManagerFactoryRef = "masterEntityManagerFactory",
//        transactionManagerRef = "masterTransactionManager"
//)
public class MasterRepositoryConfig {
}
