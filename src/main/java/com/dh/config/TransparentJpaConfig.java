package com.dh.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * Transparent JPA Configuration
 * 
 * This configuration uses the dynamic routing datasource for transparent
 * read/write splitting. Only one EntityManager and TransactionManager needed.
 */
@Configuration
@EnableJpaRepositories(
    basePackages = "com.dh.repository",
    entityManagerFactoryRef = "transparentEntityManagerFactory",
    transactionManagerRef = "transparentTransactionManager"
)
public class TransparentJpaConfig {

    @Primary
    @Bean(name = "transparentEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean transparentEntityManagerFactory(
            @Qualifier("routingDataSource") DataSource routingDataSource) {
        
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(routingDataSource);
        em.setPackagesToScan("com.dh.entity");
        em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        em.setJpaProperties(hibernateProperties());
        em.setPersistenceUnitName("transparentPU");
        
        return em;
    }

    @Primary
    @Bean(name = "transparentTransactionManager")
    public PlatformTransactionManager transparentTransactionManager(
            @Qualifier("transparentEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory.getObject());
        
        return transactionManager;
    }

    private Properties hibernateProperties() {
        Properties properties = new Properties();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        properties.put("hibernate.show_sql", "true");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.hbm2ddl.auto", "validate");
        
        return properties;
    }
}
