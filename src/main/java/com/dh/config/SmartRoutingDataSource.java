package com.dh.config;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * Smart Routing DataSource
 * 
 * This approach doesn't require readOnly = true in @Transactional
 * Instead, it analyzes the SQL statement to determine routing
 */
public class SmartRoutingDataSource extends AbstractRoutingDataSource {
    
    private static final ThreadLocal<String> OPERATION_TYPE = new ThreadLocal<>();
    
    @Override
    protected Object determineCurrentLookupKey() {
        String operationType = OPERATION_TYPE.get();
        
        if (operationType != null) {
            boolean isReadOperation = operationType.equals("READ");
            String dataSourceKey = isReadOperation ? "slave" : "master";
            
            System.out.println("🧠 SMART ROUTING: " + operationType + " operation → " + 
                             (isReadOperation ? "SLAVE" : "MASTER") + " database");
            
            return dataSourceKey;
        }
        
        // Default to master if we can't determine
        System.out.println("🧠 SMART ROUTING: UNKNOWN operation → MASTER database (safe default)");
        return "master";
    }
    
    /**
     * Set operation type for current thread
     */
    public static void setOperationType(String operationType) {
        OPERATION_TYPE.set(operationType);
    }
    
    /**
     * Clear operation type for current thread
     */
    public static void clearOperationType() {
        OPERATION_TYPE.remove();
    }
    
    /**
     * Determine operation type from SQL
     */
    public static String determineOperationType(String sql) {
        if (sql == null) return "WRITE";
        
        String upperSql = sql.trim().toUpperCase();
        
        // Read operations
        if (upperSql.startsWith("SELECT") || 
            upperSql.startsWith("SHOW") || 
            upperSql.startsWith("DESCRIBE") || 
            upperSql.startsWith("EXPLAIN")) {
            return "READ";
        }
        
        // Write operations
        if (upperSql.startsWith("INSERT") || 
            upperSql.startsWith("UPDATE") || 
            upperSql.startsWith("DELETE") || 
            upperSql.startsWith("CREATE") || 
            upperSql.startsWith("ALTER") || 
            upperSql.startsWith("DROP")) {
            return "WRITE";
        }
        
        // Default to write for safety
        return "WRITE";
    }
}
