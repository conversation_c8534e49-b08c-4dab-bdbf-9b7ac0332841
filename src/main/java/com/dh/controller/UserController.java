package com.dh.controller;

import com.dh.entity.User;
import com.dh.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    // Write operations (Master DB)
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        log.info("REST: Creating new user - {}", user.getEmail());
        try {
            User createdUser = userService.createUser(user);
            log.info("REST: User created successfully with ID: {}", createdUser.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (Exception e) {
            log.error("REST: Error creating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        log.info("REST: Updating user - ID: {}", id);
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            log.info("REST: User updated successfully");
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            log.error("REST: Error updating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        log.info("REST: Deleting user - ID: {}", id);
        try {
            userService.deleteUser(id);
            log.info("REST: User deleted successfully");
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("REST: Error deleting user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PatchMapping("/{id}/name")
    public ResponseEntity<String> updateUserName(@PathVariable Long id, @RequestParam String name) {
        log.info("REST: Updating user name - ID: {}, Name: {}", id, name);
        try {
            int result = userService.updateUserName(id, name);
            if (result > 0) {
                log.info("REST: User name updated successfully");
                return ResponseEntity.ok("User name updated successfully");
            } else {
                log.warn("REST: User not found for name update");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("REST: Error updating user name", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Read operations (Slave DB)
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        log.info("REST: Getting all users");
        try {
            List<User> users = userService.getAllUsers();
            log.info("REST: Retrieved {} users", users.size());
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("REST: Error getting all users", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        log.info("REST: Getting user by ID: {}", id);
        try {
            Optional<User> user = userService.getUserById(id);
            if (user.isPresent()) {
                log.info("REST: User found");
                return ResponseEntity.ok(user.get());
            } else {
                log.info("REST: User not found");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("REST: Error getting user by ID", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/email/{email}")
    public ResponseEntity<User> getUserByEmail(@PathVariable String email) {
        log.info("REST: Getting user by email: {}", email);
        try {
            Optional<User> user = userService.getUserByEmail(email);
            if (user.isPresent()) {
                log.info("REST: User found by email");
                return ResponseEntity.ok(user.get());
            } else {
                log.info("REST: User not found by email");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("REST: Error getting user by email", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchUsersByName(@RequestParam String name) {
        log.info("REST: Searching users by name: {}", name);
        try {
            List<User> users = userService.getUsersByNameContaining(name);
            log.info("REST: Found {} users matching name", users.size());
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("REST: Error searching users by name", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/count")
    public ResponseEntity<Long> getUserCount() {
        log.info("REST: Getting user count");
        try {
            long count = userService.getUserCount();
            log.info("REST: Total user count: {}", count);
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            log.error("REST: Error getting user count", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/recent")
    public ResponseEntity<List<User>> getRecentUsers() {
        log.info("REST: Getting recent users");
        try {
            List<User> users = userService.getUsersOrderByCreatedDate();
            log.info("REST: Retrieved {} recent users", users.size());
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("REST: Error getting recent users", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
