package com.dh.controller;

import com.dh.entity.User;
import com.dh.service.TransparentUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Transparent User Controller
 * 
 * This controller demonstrates transparent read/write splitting.
 * The application code doesn't need to know about master/slave databases.
 * Routing happens automatically based on transaction annotations.
 */
@RestController
@RequestMapping("/api/transparent/users")
@RequiredArgsConstructor
@Slf4j
public class TransparentUserController {
    
    private final TransparentUserService userService;
    
    // ========== WRITE OPERATIONS ==========
    
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        log.info("🌐 TRANSPARENT API: Creating new user - {}", user.getEmail());
        try {
            User createdUser = userService.createUser(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (Exception e) {
            log.error("❌ Error creating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        log.info("🌐 TRANSPARENT API: Updating user - ID: {}", id);
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            log.error("❌ Error updating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        log.info("🌐 TRANSPARENT API: Deleting user - ID: {}", id);
        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("❌ Error deleting user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PatchMapping("/{id}/name")
    public ResponseEntity<String> updateUserName(@PathVariable Long id, @RequestParam String name) {
        log.info("🌐 TRANSPARENT API: Updating user name - ID: {}, Name: {}", id, name);
        try {
            int result = userService.updateUserName(id, name);
            if (result > 0) {
                return ResponseEntity.ok("User name updated successfully");
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("❌ Error updating user name", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // ========== READ OPERATIONS ==========
    
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        log.info("🌐 TRANSPARENT API: Getting all users");
        try {
            List<User> users = userService.getAllUsers();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("❌ Error getting all users", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        log.info("🌐 TRANSPARENT API: Getting user by ID: {}", id);
        try {
            Optional<User> user = userService.getUserById(id);
            return user.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("❌ Error getting user by ID", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/email/{email}")
    public ResponseEntity<User> getUserByEmail(@PathVariable String email) {
        log.info("🌐 TRANSPARENT API: Getting user by email: {}", email);
        try {
            Optional<User> user = userService.getUserByEmail(email);
            return user.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("❌ Error getting user by email", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchUsersByName(@RequestParam String name) {
        log.info("🌐 TRANSPARENT API: Searching users by name: {}", name);
        try {
            List<User> users = userService.getUsersByNameContaining(name);
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("❌ Error searching users by name", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/count")
    public ResponseEntity<Long> getUserCount() {
        log.info("🌐 TRANSPARENT API: Getting user count");
        try {
            long count = userService.getUserCount();
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            log.error("❌ Error getting user count", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/recent")
    public ResponseEntity<List<User>> getRecentUsers() {
        log.info("🌐 TRANSPARENT API: Getting recent users");
        try {
            List<User> users = userService.getUsersOrderByCreatedDate();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("❌ Error getting recent users", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
