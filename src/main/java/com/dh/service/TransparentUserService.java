package com.dh.service;

import com.dh.entity.User;
import com.dh.repository.TransparentUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Transparent User Service
 * 
 * This service demonstrates transparent read/write splitting:
 * - Read operations use @Transactional(readOnly = true) → automatically routed to SLAVE
 * - Write operations use @Transactional → automatically routed to MASTER
 * - Application code doesn't need to know about master/slave setup
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransparentUserService {
    
    private final TransparentUserRepository userRepository;
    
    // ========== WRITE OPERATIONS (Auto-routed to MASTER) ==========
    
    @Transactional
    public User createUser(User user) {
        log.info("💾 TRANSPARENT WRITE: Creating user - {}", user.getEmail());
        User savedUser = userRepository.save(user);
        log.info("✅ User created with ID: {}", savedUser.getId());
        return savedUser;
    }
    
    @Transactional
    public User updateUser(User user) {
        log.info("💾 TRANSPARENT WRITE: Updating user - ID={}", user.getId());
        User updatedUser = userRepository.save(user);
        log.info("✅ User updated successfully");
        return updatedUser;
    }
    
    @Transactional
    public void deleteUser(Long id) {
        log.info("💾 TRANSPARENT WRITE: Deleting user - ID={}", id);
        userRepository.deleteById(id);
        log.info("✅ User deleted successfully");
    }
    
    @Transactional
    public int updateUserName(Long id, String name) {
        log.info("💾 TRANSPARENT WRITE: Updating user name - ID={}, Name={}", id, name);
        int result = userRepository.updateUserName(id, name);
        log.info("✅ User name updated, affected rows: {}", result);
        return result;
    }
    
    // ========== READ OPERATIONS (Auto-routed to SLAVE) ==========
    
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        log.info("📖 TRANSPARENT READ: Getting all users");
        List<User> users = userRepository.findAll();
        log.info("✅ Retrieved {} users", users.size());
        return users;
    }
    
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        log.info("📖 TRANSPARENT READ: Getting user by ID - {}", id);
        Optional<User> user = userRepository.findById(id);
        log.info("✅ User found: {}", user.isPresent());
        return user;
    }
    
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        log.info("📖 TRANSPARENT READ: Getting user by email - {}", email);
        Optional<User> user = userRepository.findByEmail(email);
        log.info("✅ User found: {}", user.isPresent());
        return user;
    }
    
    @Transactional(readOnly = true)
    public List<User> getUsersByNameContaining(String name) {
        log.info("📖 TRANSPARENT READ: Searching users by name - '{}'", name);
        List<User> users = userRepository.findByNameContaining(name);
        log.info("✅ Found {} users", users.size());
        return users;
    }
    
    @Transactional(readOnly = true)
    public long getUserCount() {
        log.info("📖 TRANSPARENT READ: Counting users");
        long count = userRepository.countAllUsers();
        log.info("✅ Total users: {}", count);
        return count;
    }
    
    @Transactional(readOnly = true)
    public List<User> getUsersOrderByCreatedDate() {
        log.info("📖 TRANSPARENT READ: Getting users ordered by created date");
        List<User> users = userRepository.findAllOrderByCreatedAtDesc();
        log.info("✅ Retrieved {} users ordered by created date", users.size());
        return users;
    }
}
