package com.dh.service;

import com.dh.entity.User;
import com.dh.repository.master.UserMasterRepository;
import com.dh.repository.slave.UserSlaveRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {
    
    private final UserMasterRepository userMasterRepository;
    private final UserSlaveRepository userSlaveRepository;
    
    // Write operations - use master database
    @Transactional("masterTransactionManager")
    public User createUser(User user) {
        log.info("Creating user in MASTER database: {}", user.getEmail());
        User savedUser = userMasterRepository.save(user);
        log.info("User created successfully in MASTER database with ID: {}", savedUser.getId());
        return savedUser;
    }
    
    @Transactional("masterTransactionManager")
    public User updateUser(User user) {
        log.info("Updating user in MASTER database: ID={}, Email={}", user.getId(), user.getEmail());
        User updatedUser = userMasterRepository.save(user);
        log.info("User updated successfully in MASTER database");
        return updatedUser;
    }
    
    @Transactional("masterTransactionManager")
    public void deleteUser(Long id) {
        log.info("Deleting user from MASTER database: ID={}", id);
        userMasterRepository.deleteById(id);
        log.info("User deleted successfully from MASTER database");
    }
    
    @Transactional("masterTransactionManager")
    public int updateUserName(Long id, String name) {
        log.info("Updating user name in MASTER database: ID={}, Name={}", id, name);
        int result = userMasterRepository.updateUserName(id, name);
        log.info("User name updated in MASTER database, affected rows: {}", result);
        return result;
    }
    
    // Read operations - use slave database
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public List<User> getAllUsers() {
        log.info("Reading all users from SLAVE database");
        List<User> users = userSlaveRepository.findAll();
        log.info("Retrieved {} users from SLAVE database", users.size());
        return users;
    }
    
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public Optional<User> getUserById(Long id) {
        log.info("Reading user by ID from SLAVE database: ID={}", id);
        Optional<User> user = userSlaveRepository.findById(id);
        log.info("User found in SLAVE database: {}", user.isPresent());
        return user;
    }
    
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        log.info("Reading user by email from SLAVE database: Email={}", email);
        Optional<User> user = userSlaveRepository.findByEmail(email);
        log.info("User found in SLAVE database: {}", user.isPresent());
        return user;
    }
    
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public List<User> getUsersByNameContaining(String name) {
        log.info("Searching users by name from SLAVE database: Name contains '{}'", name);
        List<User> users = userSlaveRepository.findByNameContaining(name);
        log.info("Found {} users in SLAVE database", users.size());
        return users;
    }
    
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public long getUserCount() {
        log.info("Counting users from SLAVE database");
        long count = userSlaveRepository.countAllUsers();
        log.info("Total users in SLAVE database: {}", count);
        return count;
    }
    
    @Transactional(value = "slaveTransactionManager", readOnly = true)
    public List<User> getUsersOrderByCreatedDate() {
        log.info("Reading users ordered by created date from SLAVE database");
        List<User> users = userSlaveRepository.findAllOrderByCreatedAtDesc();
        log.info("Retrieved {} users ordered by created date from SLAVE database", users.size());
        return users;
    }
}
