-- Create replication user for slave
CREATE USER 'replicator'@'%' IDENTIFIED BY 'replicator_password';
GRANT REPLICATION SLAVE ON *.* TO 'replicator'@'%';

-- Create application user with full privileges on master
CREATE USER 'app_user'@'%' IDENTIFIED BY 'app_password';
GRANT ALL PRIVILEGES ON masterslavedb.* TO 'app_user'@'%';

-- Create read-only user for slave
CREATE USER 'readonly_user'@'%' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON masterslavedb.* TO 'readonly_user'@'%';

FLUSH PRIVILEGES;

-- Create sample table
USE masterslavedb;

CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert some sample data
INSERT INTO users (name, email) VALUES 
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>');
