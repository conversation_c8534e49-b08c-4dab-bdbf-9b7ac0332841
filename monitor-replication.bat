@echo off
echo Starting Real-Time Replication Monitor...
echo Press Ctrl+C to stop
echo.

:loop
cls
echo ========================================
echo REAL-TIME REPLICATION MONITOR
echo Time: %date% %time%
echo ========================================

echo.
echo REPLICATION STATUS:
docker exec mysql-slave mysql -uroot -prootpassword -e "SHOW SLAVE STATUS\G" | findstr /C:"Slave_IO_Running" /C:"Slave_SQL_Running" /C:"Seconds_Behind_Master"

echo.
echo DATA SYNC CHECK:
echo Master Count:
docker exec mysql-master mysql -uroot -prootpassword -e "USE masterslavedb; SELECT COUNT(*) FROM users;" 2>nul
echo Slave Count:
docker exec mysql-slave mysql -uroot -prootpassword -e "USE masterslavedb; SELECT COUNT(*) FROM users;" 2>nul

echo.
echo BINARY LOG POSITION:
docker exec mysql-master mysql -uroot -prootpassword -e "SHOW MASTER STATUS;" | findstr /C:"mysql-bin"

timeout /t 5 /nobreak >nul
goto loop
