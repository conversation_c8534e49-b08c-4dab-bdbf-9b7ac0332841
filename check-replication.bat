@echo off
echo ========================================
echo CHECKING MYSQL MASTER-SLAVE REPLICATION
echo ========================================

echo.
echo 1. MASTER STATUS:
echo ----------------------------------------
docker exec mysql-master mysql -uroot -prootpassword -e "SHOW MASTER STATUS\G"

echo.
echo 2. SLAVE STATUS:
echo ----------------------------------------
docker exec mysql-slave mysql -uroot -prootpassword -e "SHOW SLAVE STATUS\G" | findstr /C:"Slave_IO_Running" /C:"Slave_SQL_Running" /C:"Master_Log_File" /C:"Read_Master_Log_Pos" /C:"Exec_Master_Log_Pos" /C:"Seconds_Behind_Master" /C:"Last_Error"

echo.
echo 3. MASTER DATA:
echo ----------------------------------------
docker exec mysql-master mysql -uroot -prootpassword -e "USE masterslavedb; SELECT COUNT(*) as master_count FROM users;"

echo.
echo 4. SLAVE DATA:
echo ----------------------------------------
docker exec mysql-slave mysql -uroot -prootpassword -e "USE masterslavedb; SELECT COUNT(*) as slave_count FROM users;"

echo.
echo 5. BINARY LOG FILES:
echo ----------------------------------------
docker exec mysql-master mysql -uroot -prootpassword -e "SHOW BINARY LOGS;"

echo.
echo ========================================
echo REPLICATION CHECK COMPLETED
echo ========================================
